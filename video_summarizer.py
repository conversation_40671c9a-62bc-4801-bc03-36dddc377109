#!/usr/bin/env python3
"""
video_summarizer.py

Lokale End‑to‑End‑Pipeline:
1) Extrahiert Audio aus einem Video (FFmpeg)
2) Transkribiert offline mit faster‑whisper (Whisper v3 Modelle)
3) Chunking des Transkripts (zeitsensitiv, sat<PERSON><PERSON><PERSON><PERSON><PERSON>, mit Overlap)
4) Parallele Map‑Phase: Chunk‑Zusammenfassungen via lokalem LLM (Ollama REST)
5) Reduce‑Phase: Finale Zusammenfassung + Highlights + Timeline
6) Speichert: SRT, JSON, Markdown

Voraussetzungen:
  - FFmpeg im PATH (https://ffmpeg.org)
  - Python-Pakete: pip install faster-whisper ffmpeg-python httpx rich pydantic
  - Optional (bessere JSON-Robustheit): pip install orjson
  - Ollama lokal: https://ollama.ai  (modell vorher ziehen, z.B. `ollama pull llama3.1:8b`)

Beispiel:
  python video_summarizer.py -i input.mp4 -o out \
    --whisper-model large-v3 --compute-type float16 \
    --ollama-model llama3.1:8b --concurrency 4

  Nur bereits vorhandenes Transkript zusammenfassen:
  python video_summarizer.py --transcript-json out/transcript.json --skip-transcribe \
    --ollama-model qwen2.5:7b

Hinweise:
  - Für deutsche Videos liefert `--language de` stabilere Ergebnisse.
  - `--vad` aktiviert eine Voice Activity Detection (empfohlen bei viel Stille/Rauschen).
"""
from __future__ import annotations

import argparse
import asyncio
import json
import math
import os
import re
import subprocess
import sys
from dataclasses import dataclass, asdict
from datetime import timedelta, datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

try:
    import orjson as json_fast  # type: ignore
    def dumps(o):
        return json_fast.dumps(o, option=json_fast.OPT_INDENT_2).decode()
    def loads(s: str):
        return json_fast.loads(s)
except Exception:  # pragma: no cover
    def dumps(o):
        return json.dumps(o, ensure_ascii=False, indent=2)
    def loads(s: str):
        return json.loads(s)

from rich import print
from rich.table import Table
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TimeElapsedColumn, BarColumn, TaskProgressColumn

import httpx
import ffmpeg  # ffmpeg-python
from faster_whisper import WhisperModel

console = Console()

# ========= Utilities =========

def hhmmss(seconds: float) -> str:
    return str(timedelta(seconds=round(seconds)))

@dataclass
class Segment:
    start: float
    end: float
    text: str

@dataclass
class Transcript:
    language: str
    duration: float
    segments: List[Segment]

# ========= FFmpeg =========

def extract_audio(input_video: Path, output_wav: Path, sr: int = 16000) -> None:
    """Extract mono WAV PCM16 using ffmpeg-python."""
    (
        ffmpeg
        .input(str(input_video))
        .output(str(output_wav), ac=1, ar=sr, f='wav', vn=None, loglevel='error')
        .overwrite_output()
        .run()
    )

# ========= Transcription (faster-whisper) =========

def transcribe(audio_path: Path,
               whisper_model: str = "large-v3",
               device: str = "auto",
               compute_type: str = "float16",
               language: Optional[str] = None,
               vad: bool = True,
               beam_size: int = 5) -> Transcript:
    model = WhisperModel(whisper_model, device=device, compute_type=compute_type)
    segments, info = model.transcribe(
        str(audio_path),
        language=language,
        vad_filter=True,
        vad_parameters=dict(min_silence_duration_ms=1000),
        beam_size=beam_size,
        best_of=beam_size,
        condition_on_previous_text=False,
        temperature=0.0,
    )
    segs = []
    last_end = 0.0
    for s in segments:
        # Sicherheitsnetz: non-decreasing timestamps
        start = max(float(s.start), last_end)
        end = max(float(s.end), start)
        segs.append(Segment(start=start, end=end, text=s.text.strip()))
        last_end = end
    duration = float(getattr(info, 'duration', segs[-1].end if segs else 0.0))
    lang = getattr(info, 'language', language or 'auto') or 'auto'
    return Transcript(language=lang, duration=duration, segments=segs)

# ========= Chunking =========

def split_into_sentences(text: str) -> List[str]:
    # einfache satzsegmentierung (punkt, frage-, ausrufezeichen)
    parts = re.split(r"(?<=[.!?])\s+", text.strip())
    return [p.strip() for p in parts if p.strip()]

def chunk_transcript(segments: List[Segment],
                     target_chars: int = 4000,
                     overlap_chars: int = 400) -> List[Dict[str, Any]]:
    """Chunkt zeitsensitiv: hält sich an Segmentgrenzen, versucht Sätze nicht zu zerreißen."""
    chunks: List[Dict[str, Any]] = []
    cur_text = []
    cur_start = None
    cur_end = None
    cur_len = 0

    def flush():
        nonlocal cur_text, cur_start, cur_end, cur_len
        if cur_text:
            text = " ".join(cur_text).strip()
            chunks.append({
                "start": cur_start,
                "end": cur_end,
                "text": text,
            })
        cur_text, cur_start, cur_end, cur_len = [], None, None, 0

    for seg in segments:
        seg_text = seg.text
        seg_len = len(seg_text)
        if cur_len == 0:
            cur_start = seg.start
        if cur_len + seg_len <= target_chars:
            cur_text.append(seg_text)
            cur_len += seg_len + 1
            cur_end = seg.end
        else:
            # flush current chunk
            flush()
            # start with overlap: carry last sentences of previous segment set
            # (einfach: keine harten satzschnitte mitten im segment)
            cur_start = seg.start
            cur_text = [seg_text]
            cur_len = seg_len
            cur_end = seg.end
    flush()

    # add textual overlap between neighbor chunks
    if overlap_chars > 0 and len(chunks) > 1:
        for i in range(1, len(chunks)):
            prev = chunks[i-1]
            cur = chunks[i]
            # take tail of prev text (up to overlap_chars)
            tail = prev["text"][-overlap_chars:]
            # prepend as context marker
            cur["text"] = f"[KONTEXT AUS VORHERIGEM CHUNK]\n{tail}\n[AKTUELLER CHUNK]\n{cur['text']}"
    return chunks

# ========= SRT =========

def to_srt(transcript: Transcript) -> str:
    lines = []
    for i, s in enumerate(transcript.segments, start=1):
        lines.append(str(i))
        lines.append(f"{srt_timestamp(s.start)} --> {srt_timestamp(s.end)}")
        lines.append(s.text)
        lines.append("")
    return "\n".join(lines)


def srt_timestamp(seconds: float) -> str:
    td = timedelta(seconds=seconds)
    total = int(td.total_seconds())
    h = total // 3600
    m = (total % 3600) // 60
    s = total % 60
    ms = int((td.total_seconds() - total) * 1000)
    return f"{h:02d}:{m:02d}:{s:02d},{ms:03d}"

# ========= Ollama Client =========

class OllamaClient:
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "llama3.1:8b",
                 num_ctx: int = 8192, temperature: float = 0.2, top_p: float = 0.9):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.num_ctx = num_ctx
        self.temperature = temperature
        self.top_p = top_p

    async def generate(self, prompt: str, system: Optional[str] = None, json_mode: bool = False,
                       timeout: float = 0) -> str:
        payload: Dict[str, Any] = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {"num_ctx": self.num_ctx, "temperature": self.temperature, "top_p": self.top_p},
        }
        if system:
            payload["system"] = system
        if json_mode:
            payload["format"] = "json"
        async with httpx.AsyncClient(timeout=None if timeout == 0 else httpx.Timeout(timeout)) as client:
            r = await client.post(f"{self.base_url}/api/generate", json=payload)
            r.raise_for_status()
            data = r.json()
            return data.get("response", "")

# ========= Prompting =========

MAP_SYSTEM = (
"Du bist ein präziser Analytiker. Fasse Transkript-Chunks detailliert zusammen, "
"inklusive exakter Timestamps für jeden wichtigen Punkt, ausführlicher Key Points, Zitate und Themen. "
"Die Zusammenfassung soll sowohl inhaltlich dicht als auch chronologisch korrekt sein."
)


MAP_USER_TEMPLATE = (
"ZUSAMMENFASSUNG EINES VIDEO-CHUNKS\n"
"Zeitfenster: {start}-{end}\n"
"Text:\n"""{text}"""\n"
"Aufgabe: Erstelle eine ausführliche Zusammenfassung, liste alle wichtigen Punkte mit Timestamps auf, "
"erwähne Key Points, aussagekräftige Zitate und relevante Themen. Sei präzise und chronologisch korrekt."
)


REDUCE_SYSTEM = (
"Du aggregierst alle Teilzusammenfassungen zu einer umfassenden Gesamtübersicht. "
"Die finale Zusammenfassung soll ausführlich sein, mit TL;DR, detaillierten Highlights, Key Points und Zeitangaben für das gesamte Video."
)


REDUCE_USER_TEMPLATE = (
"Erstelle eine finale, ausführliche Zusammenfassung im JSON-Format für das gesamte Video.\n"
"Füge TL;DR, detaillierte Highlights mit Zeitstempeln, vollständige Outline, Action Items und offene Fragen hinzu.\n"
"Stelle sicher, dass alle Inhalte mit Timestamps versehen und chronologisch geordnet sind.\n"
"Teil-Zusammenfassungen:\n{chunk_summaries}"
)

# ========= Summarization Pipeline =========

async def map_summarize_chunks(chunks: List[Dict[str, Any]], client: OllamaClient, concurrency: int = 4) -> List[Dict[str, Any]]:
    sem = asyncio.Semaphore(concurrency)

    async def worker(idx: int, chunk: Dict[str, Any]) -> Dict[str, Any]:
        async with sem:
            start_s = hhmmss(chunk["start"]) if chunk.get("start") is not None else "?"
            end_s = hhmmss(chunk["end"]) if chunk.get("end") is not None else "?"
            prompt = MAP_USER_TEMPLATE.format(start=start_s, end=end_s, text=chunk["text"])
            resp = await client.generate(prompt, system=MAP_SYSTEM, json_mode=True)
            # Robust JSON parsing
            try:
                data = loads(resp)
                data["_chunk_index"] = idx
                data["_time"] = f"{start_s}-{end_s}"
                return data
            except Exception:
                # try to salvage JSON object from text
                m = re.search(r"\{[\s\S]*\}", resp)
                if m:
                    try:
                        data = loads(m.group(0))
                        data["_chunk_index"] = idx
                        data["_time"] = f"{start_s}-{end_s}"
                        return data
                    except Exception:
                        pass
                # fallback: return plain summary
                return {
                    "summary": resp.strip(),
                    "key_points": [],
                    "quotes": [],
                    "topics": [],
                    "time_hint": f"{start_s}-{end_s}",
                    "_chunk_index": idx,
                    "_time": f"{start_s}-{end_s}",
                }

    tasks = [asyncio.create_task(worker(i, c)) for i, c in enumerate(chunks)]
    results = await asyncio.gather(*tasks)
    # sort by original order
    results.sort(key=lambda x: x.get("_chunk_index", 0))
    return results

async def reduce_finalize(chunk_summaries: List[Dict[str, Any]], client: OllamaClient) -> Dict[str, Any]:
    # keep only load-bearing fields to keep prompt small
    slim = [
        {
            "summary": cs.get("summary", ""),
            "key_points": cs.get("key_points", []),
            "time": cs.get("_time") or cs.get("time_hint", "")
        }
        for cs in chunk_summaries
    ]
    prompt = REDUCE_USER_TEMPLATE.format(chunk_summaries=dumps(slim))
    resp = await client.generate(prompt, system=REDUCE_SYSTEM, json_mode=True)
    try:
        return loads(resp)
    except Exception:
        m = re.search(r"\{[\s\S]*\}", resp)
        if m:
            try:
                return loads(m.group(0))
            except Exception:
                pass
        return {"tl_dr": resp.strip(), "highlights": [], "outline": [], "action_items": [], "open_questions": []}

# ========= I/O Helpers =========

def save_json(path: Path, obj: Any):
    path.write_text(dumps(obj), encoding='utf-8')

def save_markdown_summary(path: Path, meta: Dict[str, Any], final: Dict[str, Any]):
    ts = datetime.now().strftime("%Y-%m-%d %H:%M")
    lines = [
        f"# Video-Zusammenfassung",
        "",
        f"**Erstellt:** {ts}",
        f"**Datei:** {meta.get('input_basename','?')}",
        f"**Dauer:** {meta.get('duration','?')}",
        f"**Sprache:** {meta.get('language','?')}",
        f"**Whisper-Modell:** {meta.get('whisper_model','?')}  ",
        f"**LLM (Ollama):** {meta.get('ollama_model','?')}",
        "",
        "## TL;DR",
        final.get("tl_dr", "(leer)"),
        "",
        "## Highlights",
    ]
    for h in final.get("highlights", []):
        lines.append(f"- [{h.get('time','?')}] {h.get('point','')}")
    lines += ["", "## Outline"]
    for o in final.get("outline", []):
        lines.append(f"- {o}")
    if final.get("action_items"):
        lines += ["", "## Action Items"]
        for a in final.get("action_items", []):
            lines.append(f"- {a}")
    if final.get("open_questions"):
        lines += ["", "## Offene Fragen"]
        for q in final.get("open_questions", []):
            lines.append(f"- {q}")
    path.write_text("\n".join(lines), encoding='utf-8')

# ========= Main =========

def main():
    ap = argparse.ArgumentParser(description="Transkription + lokale LLM-Zusammenfassung (Map-Reduce)")
    ap.add_argument('-i', '--input', type=str, help='Video-/Audio-Datei')
    ap.add_argument('-o', '--outdir', type=str, default='out', help='Ausgabeverzeichnis')
    ap.add_argument('--language', type=str, default=None, help='Erwartete Sprache (z.B. de, en).')
    ap.add_argument('--skip-transcribe', action='store_true', help='Transkription überspringen und existierende JSON verwenden.')
    ap.add_argument('--transcript-json', type=str, default=None, help='Pfad zu existierendem transcript.json')

    # Whisper/faster-whisper
    ap.add_argument('--whisper-model', type=str, default='large-v3', help='z.B. tiny/base/small/medium/large-v3')
    ap.add_argument('--device', type=str, default='auto', help='auto/cpu/cuda')
    ap.add_argument('--compute-type', type=str, default='float16', help='float32/float16/int8_float16/int8')
    ap.add_argument('--vad', action='store_true', help='VAD-Filter aktivieren')

    # Chunking
    ap.add_argument('--target-chars', type=int, default=4000, help='Zielgröße eines Chunks (Zeichen)')
    ap.add_argument('--overlap-chars', type=int, default=400, help='Textuelles Overlap (Zeichen)')

    # Ollama
    ap.add_argument('--ollama-url', type=str, default='http://localhost:11434', help='Basis-URL von Ollama')
    ap.add_argument('--ollama-model', type=str, default='llama3.1:8b', help='z.B. llama3.1:8b, qwen2.5:7b, mistral')
    ap.add_argument('--num-ctx', type=int, default=8192, help='Kontextgröße, an Modell anpassen')
    ap.add_argument('--temperature', type=float, default=0.2)
    ap.add_argument('--top-p', type=float, default=0.9)
    ap.add_argument('--concurrency', type=int, default=4, help='Parallele Chunk-Requests')

    args = ap.parse_args()

    outdir = Path(args.outdir)
    outdir.mkdir(parents=True, exist_ok=True)

    transcript: Optional[Transcript] = None

    if not args.skip_transcribe:
        if not args.input:
            ap.error("--input ist erforderlich, wenn --skip-transcribe nicht gesetzt ist.")
        in_path = Path(args.input)
        if not in_path.exists():
            ap.error(f"Eingabedatei nicht gefunden: {in_path}")
        audio_path = outdir / "audio.wav"
        print(f"[bold]1/5 Audio extrahieren[/bold] -> {audio_path}")
        extract_audio(in_path, audio_path)

        print("[bold]2/5 Transkribieren (faster-whisper)[/bold]")
        transcript = transcribe(audio_path,
                                whisper_model=args.whisper_model,
                                device=args.device,
                                compute_type=args.compute_type,
                                language=args.language,
                                vad=args.vad)
        # Save transcript
        transcript_json = {
            "language": transcript.language,
            "duration": transcript.duration,
            "segments": [asdict(s) for s in transcript.segments],
        }
        (outdir / 'transcript.json').write_text(dumps(transcript_json), encoding='utf-8')
        (outdir / 'transcript.srt').write_text(to_srt(transcript), encoding='utf-8')
        print(f"Gespeichert: {outdir/'transcript.json'} & {outdir/'transcript.srt'}")
    else:
        # load existing transcript
        tj = args.transcript_json or (Path(args.input).with_suffix('') if args.input else outdir/ 'transcript.json')
        tj = Path(tj)
        if not tj.exists():
            ap.error(f"transcript.json nicht gefunden: {tj}")
        data = loads(tj.read_text(encoding='utf-8'))
        segs = [Segment(**s) for s in data["segments"]]
        transcript = Transcript(language=data.get('language','auto'), duration=data.get('duration',0.0), segments=segs)
        print(f"Transkript geladen von {tj}")

    assert transcript is not None

    print("[bold]3/5 Chunking[/bold]")
    chunks = chunk_transcript(transcript.segments, target_chars=args.target_chars, overlap_chars=args.overlap_chars)
    (outdir / 'chunks.json').write_text(dumps(chunks), encoding='utf-8')
    print(f"Chunks: {len(chunks)} (gespeichert in {outdir/'chunks.json'})")

    print("[bold]4/5 Map-Phase: Chunk-Zusammenfassungen (Ollama)[/bold]")
    client = OllamaClient(base_url=args.ollama_url, model=args.ollama_model, num_ctx=args.num_ctx,
                          temperature=args.temperature, top_p=args.top_p)

    chunk_summaries = asyncio.run(map_summarize_chunks(chunks, client, concurrency=args.concurrency))
    (outdir / 'chunk_summaries.json').write_text(dumps(chunk_summaries), encoding='utf-8')

    print("[bold]5/5 Reduce-Phase: Finale Zusammenfassung[/bold]")
    final = asyncio.run(reduce_finalize(chunk_summaries, client))
    (outdir / 'final_summary.json').write_text(dumps(final), encoding='utf-8')

    meta = {
        'input_basename': Path(args.input).name if args.input else 'n/a',
        'duration': hhmmss(transcript.duration),
        'language': transcript.language,
        'whisper_model': args.whisper_model,
        'ollama_model': args.ollama_model,
    }
    save_markdown_summary(outdir / 'final_summary.md', meta, final)

    # Abschlussübersicht
    tbl = Table(title="Fertig")
    tbl.add_column("Artefakt")
    tbl.add_column("Pfad")
    for name in ["transcript.srt", "transcript.json", "chunks.json", "chunk_summaries.json", "final_summary.json", "final_summary.md"]:
        p = outdir / name
        if p.exists():
            tbl.add_row(name, str(p))
    console.print(tbl)

if __name__ == '__main__':
    main()
